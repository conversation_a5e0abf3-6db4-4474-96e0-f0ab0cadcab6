/* RecipeAdder component - Unified Design System */
.recipe-adder-container {
  padding: 20px; /* Consistent padding */
  border-radius: 12px; /* Consistent border radius */
  background-color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* Consistent shadow */
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.recipe-adder-container h2 {
  margin: 0 0 20px 0; /* Consistent margin */
  color: #2c3e50; /* Consistent color */
  text-align: center;
  flex-shrink: 0;
  font-weight: 600; /* Consistent font weight */
  font-size: 1.5rem; /* Consistent font size */
  background-color: #f8f9fa; /* Consistent background */
  padding: 16px; /* Consistent padding */
  border-radius: 8px; /* Consistent border radius */
  border-bottom: 2px solid #e9ecef; /* Consistent border */
  line-height: 1.3; /* Better line height */
}

.recipe-adder-form {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px; /* Consistent padding */
  display: flex;
  flex-direction: column;
}

.form-sections-container {
  display: flex;
  gap: 16px; /* Consistent gap */
  flex: 1;
  min-height: 0;
}

.recipe-adder-form .form-section {
  background-color: #fff;
  padding: 16px; /* Consistent padding */
  border-radius: 8px; /* Consistent border radius */
  border: 1px solid #e9ecef; /* Consistent border color */
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.recipe-details-section {
  flex: 0 0 48%; /* Consistent proportions */
}

.ingredients-section {
  flex: 0 0 44%; /* Consistent proportions */
}

.recipe-adder-form .form-section h3 {
  color: #2c3e50; /* Consistent color */
  border-bottom: 1px solid #e9ecef; /* Consistent border */
  padding-bottom: 8px; /* Consistent padding */
  margin: 0 0 16px 0; /* Consistent margin */
  font-size: 1.1rem; /* Consistent font size */
  font-weight: 600; /* Consistent font weight */
  line-height: 1.3; /* Better line height */
}

.recipe-adder-form .form-group {
  margin-bottom: 16px; /* Consistent margin */
}

.recipe-adder-form label {
  display: block;
  margin-bottom: 6px; /* Consistent margin */
  font-weight: 600; /* Consistent font weight */
  color: #2c3e50; /* Consistent color */
  font-size: 0.9rem; /* Consistent font size */
  line-height: 1.4; /* Better line height */
}

.recipe-adder-form input[type="text"],
.recipe-adder-form input[type="number"],
.recipe-adder-form textarea,
.recipe-adder-form select {
  width: 100%;
  padding: 10px 12px; /* Consistent padding */
  border: 1px solid #dee2e6; /* Consistent border color */
  border-radius: 6px; /* Consistent border radius */
  box-sizing: border-box;
  font-size: 0.9rem; /* Consistent font size */
  background-color: white;
  transition: all 0.2s ease; /* Smooth transitions */
  line-height: 1.4; /* Better line height */
}

.recipe-adder-form input[type="text"]:focus,
.recipe-adder-form input[type="number"]:focus,
.recipe-adder-form textarea:focus,
.recipe-adder-form select:focus {
  outline: none;
  border-color: #26a69a; /* Consistent focus color */
  box-shadow: 0 0 0 3px rgba(38, 166, 154, 0.1); /* Consistent focus shadow */
}

.recipe-adder-form input[type="file"] {
  padding: 8px; /* Consistent padding */
  font-size: 0.9rem; /* Consistent font size */
  border: 1px solid #dee2e6; /* Consistent border */
  border-radius: 6px; /* Consistent border radius */
}

.recipe-adder-form textarea {
  resize: vertical;
  min-height: 80px; /* Better minimum height */
}

.recipe-adder-form .image-preview {
  max-width: 120px;
  max-height: 120px;
  margin-top: 12px; /* Consistent margin */
  border: 1px solid #dee2e6; /* Consistent border */
  border-radius: 6px; /* Consistent border radius */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.recipe-adder-form .months-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Consistent gap */
  margin-top: 8px; /* Consistent margin */
}

.recipe-adder-form .month-button {
  padding: 8px 12px; /* Consistent padding */
  border: 1px solid #dee2e6; /* Consistent border */
  border-radius: 6px; /* Consistent border radius */
  background-color: #f8f9fa; /* Consistent background */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  font-size: 0.85rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
}

.recipe-adder-form .month-button.selected {
  background-color: #26a69a; /* Consistent brand color */
  color: white;
  border-color: #26a69a;
  box-shadow: 0 2px 8px rgba(38, 166, 154, 0.2); /* Subtle shadow */
}

.recipe-adder-form .month-button:hover:not(.selected) {
  background-color: #e9ecef; /* Consistent hover color */
  border-color: #adb5bd;
}

.recipe-adder-form input[type="checkbox"] {
  margin-right: 8px; /* Consistent margin */
  vertical-align: middle;
  transform: scale(1.1); /* Slightly larger for better usability */
}

.ingredient-search-results {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Consistent gap */
  margin: 12px 0 16px 0; /* Consistent margin */
  max-height: 150px; /* Limit height and make scrollable if needed */
  overflow-y: auto;
  padding: 12px; /* Consistent padding */
  border: 1px solid #e9ecef; /* Consistent border */
  border-radius: 6px; /* Consistent border radius */
  background-color: #f8f9fa; /* Subtle background */
}

.ingredient-add-button {
  padding: 6px 12px; /* Consistent padding */
  background-color: #26a69a; /* Consistent brand color */
  color: white;
  border: none;
  border-radius: 4px; /* Consistent border radius */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  font-size: 0.85rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
}
  margin: 2px;
}

.ingredient-add-button:hover {
  background-color: #0056b3;
}

.ingredient-add-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.selected-ingredients-list {
  list-style-type: none;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}

.selected-ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  margin-bottom: 4px;
  background-color: #fdfdfd;
  font-size: 0.8rem;
}

.selected-ingredient-item span {
  flex-grow: 1;
}

.ingredient-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.ingredient-controls .ingredient-quantity {
  width: 60px;
  padding: 4px;
  font-size: 0.8rem;
}

.ingredient-controls .ingredient-unit {
  padding: 4px;
  min-width: 70px;
  font-size: 0.8rem;
}

.ingredient-controls .ingredient-divisible-label {
  font-weight: normal;
  font-size: 0.9em;
  display: flex;
  align-items: center;
}

.ingredient-controls .remove-ingredient-button {
  padding: 4px 8px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.8rem;
}

.ingredient-controls .remove-ingredient-button:hover {
  background-color: #c82333;
}

.feedback-message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.feedback-message.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-message.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.submit-recipe-button {
  display: block;
  width: 100%;
  padding: 14px 20px; /* Consistent padding */
  background-color: #26a69a; /* Consistent brand color */
  color: white;
  border: none;
  border-radius: 8px; /* Consistent border radius */
  font-size: 1rem; /* Consistent font size */
  font-weight: 600; /* Consistent font weight */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(38, 166, 154, 0.2); /* Subtle shadow */
  text-transform: uppercase; /* Professional look */
  letter-spacing: 0.5px; /* Better spacing */
}

.submit-recipe-button:hover {
  background-color: #229085; /* Darker shade */
  transform: translateY(-2px); /* Subtle lift effect */
  box-shadow: 0 6px 20px rgba(38, 166, 154, 0.3); /* Enhanced shadow */
}

.submit-recipe-button:disabled {
  background-color: #adb5bd; /* Consistent disabled color */
  cursor: not-allowed;
  transform: none; /* Remove hover effects */
  box-shadow: none; /* Remove shadow */
}

.ingredient-quantity-input {
  width: 80px; /* Slightly wider */
  padding: 8px; /* Consistent padding */
  border: 1px solid #dee2e6; /* Consistent border */
  border-radius: 4px; /* Consistent border radius */
  box-sizing: border-box;
  font-size: 0.85rem; /* Consistent font size */
  margin-left: 8px; /* Consistent margin */
  transition: border-color 0.2s ease; /* Smooth transitions */
}

.ingredient-quantity-input:focus {
  outline: none;
  border-color: #26a69a; /* Consistent focus color */
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.1); /* Consistent focus shadow */
}
