/* Recipe page specific styles */
.recipe-container {
  max-width: 95vw;
  margin: 0 auto;
  padding: 10px;
  box-sizing: border-box;
  height: calc(100vh - 60px); /* Account for navbar height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent scrolling */
  position: fixed; /* Make completely rigid */
  top: 60px; /* Account for navbar */
  left: 2.5vw;
  right: 2.5vw;
  bottom: 0;
  background-color: #f5f5f5;
}

.recipe-main-layout {
  display: flex;
  gap: 15px;
  height: 100%;
  width: 100%;
  align-items: stretch;
}

.recipe-adder-section {
  flex: 0 0 45%; /* Reduced from 60% to give more space to recipes */
  display: flex;
  flex-direction: column;
}

.recipe-list-section {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 15px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header styles to match ingredient page */
.ingredient-header {
  background-color: #26a69a;
  color: white;
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.ingredient-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: bold;
}

.ingredient-header p {
  margin: 10px 0 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.recipe-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Center the "Your Recipes" heading */
.recipe-list h2 {
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

/* Make recipe cards scrollable with single column for larger cards */
.recipe-cards {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 10px 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  align-content: start;
}

/* Recipe cards with dynamic height based on expanded state */
.recipe-cards .recipe-card.collapsed {
  min-height: 300px;
  max-height: none;
  width: 100%;
}

.recipe-cards .recipe-card.expanded {
  min-height: 450px;
  max-height: none;
  width: 100%;
}

/* Compact recipe card headers */
.recipe-cards .recipe-header {
  padding: 10px 15px;
}

.recipe-cards .recipe-header h3 {
  font-size: 1.1rem;
  margin-bottom: 6px;
}

.recipe-cards .recipe-meta {
  gap: 8px;
  font-size: 0.8rem;
}

/* Compact recipe card body */
.recipe-cards .recipe-body {
  padding: 8px 15px;
}

.recipe-cards .recipe-ingredients h4 {
  font-size: 1rem;
  margin-bottom: 6px;
}

.recipe-cards .recipe-ingredients li {
  font-size: 0.85rem;
  margin-bottom: 3px;
}

/* Compact recipe card footer */
.recipe-cards .recipe-footer {
  padding: 8px 15px;
  margin-top: 8px;
}

.recipe-cards .recipe-actions button {
  padding: 4px 8px;
  font-size: 0.8rem;
}

/* Override image uploader height based on card state */
.recipe-cards .recipe-card.collapsed .recipe-image-container {
  height: 180px !important;
  margin-bottom: 8px !important;
}

.recipe-cards .recipe-card.expanded .recipe-image-container {
  height: 250px !important;
  margin-bottom: 12px !important;
}

/* Make recipe card text more compact */
.recipe-cards .recipe-instructions {
  margin-bottom: 8px;
}

.recipe-cards .recipe-instructions p {
  font-size: 0.8rem;
  line-height: 1.3;
  margin-bottom: 4px;
}

/* Hide the original recipe-header styles now that we're using the new header */
.recipe-header-container {
  display: none;
}

/* ServingSizeSelector styling - center the components */
.serving-size-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.serving-button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.serving-button.active {
  background-color: #4caf50;
  color: white;
  border-color: #4caf50;
}

/* Create a container for the action buttons to keep them together */
.recipe-action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}

.rounding-button {
  background-color: #4a7c59;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0.5rem 1rem;
}

.rounding-button:hover {
  background-color: #386641;
}

.rounding-button:disabled {
  background-color: #95b595;
  cursor: not-allowed;
}

.divisibility-button {
  background-color: #f0ad4e;
  color: white;
  border: none;
  border-radius: 4px; 
  cursor: pointer;
  transition: all 0.2s;
  padding: 0.5rem 1rem;
}

.divisibility-button:hover {
  background-color: #e09e41;        
}

.divisibility-button:disabled {
  background-color: #f0ad4e;
  cursor: not-allowed;
}

.generate-servings-button {
  background-color: #4e8ff0;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0.5rem 1rem;
}

.generate-servings-button:hover {
  background-color: #4181e0;
}

.generate-servings-button:disabled {
  background-color: #95b595;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .recipe-container {
    padding: 10px;
  }
  
  .ingredient-header {
    padding: 20px;
  }
  
  .recipe-action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .rounding-button,
  .divisibility-button,
  .generate-servings-button {
    width: 100%;
  }
}
